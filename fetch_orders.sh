#!/bin/bash

# MEXC API Order Fetcher for FULA/USDT
# Fetches executed or partially executed orders for a specific date
# Usage: ./fetch_orders.sh YYYY-MM-DD
# Example: ./fetch_orders.sh 2024-01-15

set -e  # Exit on any error

# Configuration
API_BASE_URL="https://api.mexc.com"
SYMBOL="FULAUSDT"
LIMIT=1000  # Maximum allowed by MEXC API

# Check if date parameter is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 YYYY-MM-DD"
    echo "Example: $0 2024-01-15"
    exit 1
fi

DATE_INPUT="$1"

# Validate date format
if ! date -d "$DATE_INPUT" >/dev/null 2>&1; then
    echo "Error: Invalid date format. Please use YYYY-MM-DD"
    exit 1
fi

# Calculate start and end timestamps (in milliseconds)
START_TIME=$(date -d "$DATE_INPUT 00:00:00 UTC" +%s)000
END_TIME=$(date -d "$DATE_INPUT 23:59:59 UTC" +%s)999

# API credentials (set these as environment variables)
if [ -z "$MEXC_API_KEY" ] || [ -z "$MEXC_SECRET_KEY" ]; then
    echo "Error: Please set MEXC_API_KEY and MEXC_SECRET_KEY environment variables"
    echo "Example:"
    echo "export MEXC_API_KEY='your_api_key_here'"
    echo "export MEXC_SECRET_KEY='your_secret_key_here'"
    exit 1
fi

# Function to generate HMAC SHA256 signature
generate_signature() {
    local query_string="$1"
    echo -n "$query_string" | openssl dgst -sha256 -hmac "$MEXC_SECRET_KEY" | cut -d' ' -f2
}

# Function to make API request
make_api_request() {
    local endpoint="$1"
    local query_params="$2"
    
    # Add timestamp
    local timestamp=$(date +%s)000
    local full_params="${query_params}&timestamp=${timestamp}"
    
    # Generate signature
    local signature=$(generate_signature "$full_params")
    local signed_params="${full_params}&signature=${signature}"
    
    # Make the request
    curl -s -H "X-MEXC-APIKEY: $MEXC_API_KEY" \
         -H "Content-Type: application/json" \
         "${API_BASE_URL}${endpoint}?${signed_params}"
}

# Function to convert timestamp to readable date
timestamp_to_date() {
    local timestamp="$1"
    # Remove last 3 digits to convert from milliseconds to seconds
    local seconds=$((timestamp / 1000))
    date -d "@$seconds" '+%Y-%m-%d %H:%M:%S UTC'
}

# Function to filter executed/partially executed orders
filter_executed_orders() {
    local json_data="$1"
    echo "$json_data" | jq -r '
        map(select(.status == "FILLED" or .status == "PARTIALLY_FILLED")) |
        map({
            orderId: .orderId,
            clientOrderId: .clientOrderId,
            symbol: .symbol,
            side: .side,
            type: .type,
            price: .price,
            origQty: .origQty,
            executedQty: .executedQty,
            cummulativeQuoteQty: .cummulativeQuoteQty,
            status: .status,
            timeInForce: .timeInForce,
            time: .time,
            updateTime: .updateTime
        }) |
        .[]'
}

# Create output filename
OUTPUT_FILE="fula_usdt_orders_${DATE_INPUT}.csv"

echo "Fetching FULA/USDT orders for date: $DATE_INPUT"
echo "Start time: $(timestamp_to_date $START_TIME)"
echo "End time: $(timestamp_to_date $END_TIME)"
echo "Output file: $OUTPUT_FILE"

# Prepare query parameters
QUERY_PARAMS="symbol=${SYMBOL}&startTime=${START_TIME}&endTime=${END_TIME}&limit=${LIMIT}"

# Make API request
echo "Making API request..."
API_RESPONSE=$(make_api_request "/api/v3/allOrders" "$QUERY_PARAMS")

# Check if response contains error
if echo "$API_RESPONSE" | jq -e '.code' >/dev/null 2>&1; then
    ERROR_CODE=$(echo "$API_RESPONSE" | jq -r '.code')
    ERROR_MSG=$(echo "$API_RESPONSE" | jq -r '.msg // "Unknown error"')
    echo "API Error: Code $ERROR_CODE - $ERROR_MSG"
    exit 1
fi

# Check if response is valid JSON array
if ! echo "$API_RESPONSE" | jq -e 'type == "array"' >/dev/null 2>&1; then
    echo "Error: Invalid API response format"
    echo "Response: $API_RESPONSE"
    exit 1
fi

# Filter for executed/partially executed orders
FILTERED_ORDERS=$(filter_executed_orders "$API_RESPONSE")

# Count total and filtered orders
TOTAL_ORDERS=$(echo "$API_RESPONSE" | jq '. | length')
EXECUTED_ORDERS_COUNT=$(echo "$FILTERED_ORDERS" | jq -s '. | length')

echo "Total orders found: $TOTAL_ORDERS"
echo "Executed/Partially executed orders: $EXECUTED_ORDERS_COUNT"

# Create CSV header
CSV_HEADER="Order ID,Client Order ID,Symbol,Side,Type,Price,Original Qty,Executed Qty,Cumulative Quote Qty,Status,Time In Force,Order Time,Update Time"

# Create CSV file
echo "$CSV_HEADER" > "$OUTPUT_FILE"

# Convert filtered orders to CSV format
if [ "$EXECUTED_ORDERS_COUNT" -gt 0 ]; then
    echo "$FILTERED_ORDERS" | jq -r '
        [.orderId, .clientOrderId, .symbol, .side, .type, .price, .origQty, .executedQty, .cummulativeQuoteQty, .status, .timeInForce, .time, .updateTime] |
        @csv
    ' >> "$OUTPUT_FILE"
    
    echo "Successfully saved $EXECUTED_ORDERS_COUNT executed/partially executed orders to $OUTPUT_FILE"
else
    echo "No executed or partially executed orders found for the specified date."
fi

# Display summary
echo ""
echo "=== Summary ==="
echo "Date: $DATE_INPUT"
echo "Symbol: $SYMBOL"
echo "Total orders: $TOTAL_ORDERS"
echo "Executed/Partially executed: $EXECUTED_ORDERS_COUNT"
echo "Output file: $OUTPUT_FILE"

# Show first few lines of the CSV file if it has data
if [ "$EXECUTED_ORDERS_COUNT" -gt 0 ]; then
    echo ""
    echo "=== First 5 rows of output ==="
    head -n 6 "$OUTPUT_FILE"
fi
