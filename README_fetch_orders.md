# MEXC API Order Fetcher for FULA/USDT

This script fetches executed or partially executed orders for the FULA/USDT trading pair from MEXC exchange for a specific date and saves them to a CSV file.

## Prerequisites

1. **API Credentials**: You need MEXC API credentials with `SPOT_DEAL_READ` permission
2. **Dependencies**: The script requires the following tools:
   - `curl` - for making HTTP requests
   - `jq` - for JSON processing
   - `openssl` - for signature generation
   - `date` - for timestamp calculations

### Installing Dependencies

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install curl jq openssl coreutils
```

**CentOS/RHEL:**
```bash
sudo yum install curl jq openssl coreutils
```

**macOS:**
```bash
brew install curl jq openssl coreutils
```

## Setup

1. **Set Environment Variables**: Export your MEXC API credentials:
   ```bash
   export MEXC_API_KEY="your_api_key_here"
   export MEXC_SECRET_KEY="your_secret_key_here"
   ```

2. **Make Script Executable**:
   ```bash
   chmod +x fetch_orders.sh
   ```

## Usage

```bash
./fetch_orders.sh YYYY-MM-DD
```

### Examples

```bash
# Fetch orders for January 15, 2024
./fetch_orders.sh 2024-01-15

# Fetch orders for today
./fetch_orders.sh $(date +%Y-%m-%d)

# Fetch orders for yesterday
./fetch_orders.sh $(date -d "yesterday" +%Y-%m-%d)
```

## Output

The script creates a CSV file named `fula_usdt_orders_YYYY-MM-DD.csv` with the following columns:

- **Order ID**: Unique order identifier
- **Client Order ID**: User-defined order ID
- **Symbol**: Trading pair (FULAUSDT)
- **Side**: BUY or SELL
- **Type**: Order type (LIMIT, MARKET, etc.)
- **Price**: Order price
- **Original Qty**: Original order quantity
- **Executed Qty**: Executed quantity
- **Cumulative Quote Qty**: Total quote asset amount
- **Status**: FILLED or PARTIALLY_FILLED
- **Time In Force**: Order time in force
- **Order Time**: Order creation timestamp
- **Update Time**: Last update timestamp

## Features

- **Date Filtering**: Fetches orders only for the specified date (00:00:00 to 23:59:59 UTC)
- **Status Filtering**: Only includes executed (FILLED) or partially executed (PARTIALLY_FILLED) orders
- **Error Handling**: Comprehensive error checking for API responses and invalid inputs
- **Secure Authentication**: Uses HMAC SHA256 signature for API authentication
- **Summary Report**: Displays summary statistics after execution

## API Limits

- **Rate Limit**: 10 requests per 10 seconds (IP-based)
- **Max Orders**: 1000 orders per request (MEXC API limit)
- **Date Range**: Maximum 7 days of historical data can be queried

## Troubleshooting

### Common Errors

1. **"API credentials not set"**
   - Ensure `MEXC_API_KEY` and `MEXC_SECRET_KEY` environment variables are set

2. **"Invalid date format"**
   - Use YYYY-MM-DD format (e.g., 2024-01-15)

3. **"API Error: Code 700002"**
   - Invalid signature - check your secret key

4. **"API Error: Code 700006"**
   - IP not whitelisted - add your IP to MEXC API whitelist

5. **"No executed orders found"**
   - No trades occurred on the specified date

### Debug Mode

To see detailed API responses, modify the script to add debug output:
```bash
# Add this line after the API request
echo "API Response: $API_RESPONSE" >&2
```

## Security Notes

- Never hardcode API credentials in the script
- Use environment variables or secure credential management
- Ensure your API key has minimal required permissions
- Consider IP whitelisting for additional security

## Example Output

```
Fetching FULA/USDT orders for date: 2024-01-15
Start time: 2024-01-15 00:00:00 UTC
End time: 2024-01-15 23:59:59 UTC
Output file: fula_usdt_orders_2024-01-15.csv
Making API request...
Total orders found: 25
Executed/Partially executed orders: 18

=== Summary ===
Date: 2024-01-15
Symbol: FULAUSDT
Total orders: 25
Executed/Partially executed: 18
Output file: fula_usdt_orders_2024-01-15.csv
```
